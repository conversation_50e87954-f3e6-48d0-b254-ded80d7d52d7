#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎛️ لوحة التحكم المركزية المطورة
تشغيل مباشر بدون نافذة الأوامر
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي إلى مسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    # استيراد المكتبات المطلوبة
    import customtkinter as ctk
    from tkinter import messagebox
    
    # تعيين المظهر
    ctk.set_appearance_mode("light")
    ctk.set_default_color_theme("blue")
    
    # إنشاء النافذة الرئيسية
    root = ctk.CTk()
    root.title("🎛️ لوحة التحكم المركزية المطورة - نظام المحاسبة الشامل")
    root.geometry("1400x900")
    
    # محاولة ملء الشاشة
    try:
        root.state('zoomed')
    except:
        try:
            root.attributes('-zoomed', True)
        except:
            pass
    
    # استيراد لوحة التحكم
    from ui.central_control_panel import CentralControlPanel
    
    # إنشاء لوحة التحكم
    control_panel = CentralControlPanel(root)
    
    # رسالة ترحيب
    welcome_message = """
🎉 مرحباً بك في لوحة التحكم المركزية المطورة!

✨ الميزات الجديدة:
• 🧾 إعدادات الفواتير المتقدمة مع قوالب متعددة
• 💰 نظام الرواتب والضرائب الشامل
• 🏪 إدارة المخازن مع تكامل الباركود
• 👥 إدارة المستخدمين والصلاحيات المفصلة
• 🔧 التحكم الكامل في الموديلات
• 💾 نظام النسخ الاحتياطي المتقدم
• 📊 استيراد وتصدير البيانات
• 🎨 تخصيص الواجهة والثيمات
• 🛡️ نظام الأمان المتطور
• 🔢 الأرقام التسلسلية القابلة للتخصيص
• ⚙️ إعدادات النظام الشاملة

🚀 استكشف جميع الأقسام واستمتع بالتحكم الكامل في نظامك!
    """
    
    # عرض رسالة الترحيب
    messagebox.showinfo("🎛️ لوحة التحكم المطورة", welcome_message)
    
    # بدء حلقة الأحداث
    root.mainloop()
    
except ImportError as e:
    # في حالة عدم وجود customtkinter
    import tkinter as tk
    from tkinter import messagebox
    
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    error_msg = f"""
❌ خطأ في الاستيراد:
{str(e)}

🔧 الحل:
1. تثبيت customtkinter:
   pip install customtkinter

2. أو تشغيل الأمر التالي في Command Prompt:
   python -m pip install customtkinter

3. ثم أعد تشغيل البرنامج

📁 تأكد من وجود الملفات التالية:
• ui/central_control_panel.py
• ui/advanced_sections.py
• ui/advanced_sections_part2.py
• ui/control_panel_integration.py
• themes/modern_theme.py
• config/settings.py
    """
    
    messagebox.showerror("خطأ في التشغيل", error_msg)
    
except Exception as e:
    # في حالة أي خطأ آخر
    import tkinter as tk
    from tkinter import messagebox
    import traceback
    
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    error_msg = f"""
❌ حدث خطأ غير متوقع:
{str(e)}

🔍 تفاصيل الخطأ:
{traceback.format_exc()}

💡 نصائح لحل المشكلة:
• تأكد من تثبيت Python بشكل صحيح
• تأكد من تثبيت customtkinter: pip install customtkinter
• تأكد من وجود جميع الملفات المطلوبة
• تحقق من صحة مسارات الملفات
• تأكد من صلاحيات الكتابة في مجلد المشروع

📞 للمساعدة: تأكد من وجود جميع ملفات المشروع في نفس المجلد
    """
    
    messagebox.showerror("خطأ في التشغيل", error_msg)
